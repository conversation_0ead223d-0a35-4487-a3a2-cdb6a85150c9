; ModuleID = 'main'
source_filename = "main"

@format_str = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1

declare i32 @printf(ptr, ...)

define i32 @main() {
entry:
  %call_add = call i32 @add(i32 5, i32 10)
  %printf_call = call i32 (ptr, ...) @printf(ptr @format_str, i32 %call_add)
  ret i32 0
}

define i32 @add(i32 %0, i32 %1) {
entry:
  %a = alloca i32, align 4
  store i32 %0, ptr %a, align 4
  %b = alloca i32, align 4
  store i32 %1, ptr %b, align 4
  %a1 = load i32, ptr %a, align 4
  %b2 = load i32, ptr %b, align 4
  %add = add i32 %a1, %b2
  ret i32 %add
  ret i32 0
}
